# 白名单功能测试指南

## 功能概述

已完成的白名单功能包括：

1. **数据库表结构**：
   - SQLite: `whitelist` 表，包含 `id`, `app_name`, `uuid`, `created_at`, `updated_at` 字段
   - MongoDB: `whitelist` 集合，包含相同字段结构

2. **API接口**：
   - `GET /check/Uuidwhitelist/uuidInWhitelist` - UUID白名单检查（原有接口，已更新为查询数据库）
   - `POST /whitelist/add` - 添加到白名单
   - `DELETE /whitelist/remove` - 从白名单移除
   - `GET /whitelist/list` - 获取白名单记录
   - `GET /whitelist/check` - 检查白名单状态（详细版本）

## 测试步骤

### 1. 启动服务器
```bash
./iaa-gamelog.exe
```

### 2. 测试UUID白名单检查（原接口）
```bash
# 检查不存在的UUID（应返回 data: 0）
curl -X GET "http://localhost:8080/check/Uuidwhitelist/uuidInWhitelist?uuid=test123&app_name=testapp&signature=your_signature"

# 注意：需要正确的签名验证
```

### 3. 添加UUID到白名单
```bash
curl -X POST "http://localhost:8080/whitelist/add?signature=your_signature" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "testapp",
    "uuid": "test123"
  }'
```

### 4. 再次检查UUID（应返回 data: 1）
```bash
curl -X GET "http://localhost:8080/check/Uuidwhitelist/uuidInWhitelist?uuid=test123&app_name=testapp&signature=your_signature"
```

### 5. 获取白名单记录列表
```bash
curl -X GET "http://localhost:8080/whitelist/list?app_name=testapp&limit=10&offset=0&signature=your_signature"
```

### 6. 检查白名单状态（详细版本）
```bash
curl -X GET "http://localhost:8080/whitelist/check?uuid=test123&app_name=testapp&signature=your_signature"
```

### 7. 从白名单移除UUID
```bash
curl -X DELETE "http://localhost:8080/whitelist/remove?uuid=test123&app_name=testapp&signature=your_signature"
```

### 8. 最后再次检查UUID（应返回 data: 0）
```bash
curl -X GET "http://localhost:8080/check/Uuidwhitelist/uuidInWhitelist?uuid=test123&app_name=testapp&signature=your_signature"
```

## 签名验证

所有白名单相关的API都需要签名验证。请确保在请求中包含正确的签名参数。

## 数据库支持

- **SQLite**: 自动创建 `whitelist` 表
- **MongoDB**: 自动创建 `whitelist` 集合并建立索引

## API响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": 1  // 1表示在白名单中，0表示不在白名单中
}
```

### 错误响应
```json
{
  "code": -1,
  "msg": "错误信息"
}
```

## 注意事项

1. 所有白名单API都需要签名验证
2. `app_name` 和 `uuid` 都是必需参数
3. 数据库中使用 `(app_name, uuid)` 作为唯一约束
4. 支持SQLite和MongoDB两种数据库
5. 原有的 `UuidInWhitelist` 接口已更新为查询数据库，返回格式简化为 `data: 1` 或 `data: 0`
