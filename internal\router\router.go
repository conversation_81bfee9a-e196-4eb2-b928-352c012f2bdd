package router

import (
	"log"
	"net/http"

	"iaa-gamelog/internal/config"
	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/handlers"
	"iaa-gamelog/internal/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(appConfig *config.Config, db database.DatabaseInterface) *gin.Engine {
	// 设置Gin为发布模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	r := gin.Default()

	// 配置CORS中间件
	if appConfig.Server.CORS.Enabled {
		corsConfig := cors.DefaultConfig()
		corsConfig.AllowOrigins = appConfig.Server.CORS.AllowedOrigins
		corsConfig.AllowMethods = appConfig.Server.CORS.AllowedMethods
		corsConfig.AllowHeaders = appConfig.Server.CORS.AllowedHeaders
		r.Use(cors.New(corsConfig))
		log.Println("CORS中间件已启用")
	}

	// BaseNet API路由 - 游戏配置相关
	common := r.Group("/common")
	{
		// 游戏配置
		common.GET("/config/info", handlers.GetLaunchConfig)      // BMS_LAUNCH_CONFIG
		common.GET("/config/share_list", handlers.GetShareConfig) // BMS_SHARE_CONFIG

		// 数据存储
		common.POST("/game-data/s-save", middleware.VerifySignature(appConfig), handlers.DataSave(db)) // DATA_SAVE (需要签名验证)
		common.GET("/game-data/get", handlers.DataGet(db))                                             // DATA_GET
		common.GET("/game-data/multi-get", handlers.DataMultiGet(db))                                  // DATA_MULTIGET
	}

	// 工具类接口
	check := r.Group("/check")
	{
		check.GET("/time", handlers.GetServerTime)                                                         // BMS_SERVER_TIME
		check.GET("/ip/is", handlers.CheckIP)                                                              // BMS_IP_IS_ENABLE
		check.GET("/login-code", handlers.CheckLoginCode)                                                  // LOGINCODE
		check.GET("/uuidInWhitelist", middleware.VerifySignature(appConfig), handlers.UuidInWhitelist(db)) // UUID_IN_WHITE_LIST
	}

	// 统计上报API路由
	statistics := r.Group("/statistics")
	{
		statistics.POST("/login_log", handlers.LoginLog(db))   // BMS_LOGIN_LOG
		statistics.POST("/game", handlers.GameStats(db))       // BMS_GAME
		statistics.POST("/ad/show", handlers.AdShow(db))       // BMS_AD_SHOW
		statistics.POST("/ad/hit", handlers.AdHit(db))         // BMS_AD_HIT
		statistics.POST("/share/show", handlers.ShareShow(db)) // BMS_SHARE_SHOW
		statistics.POST("/hint", handlers.Hint(db))            // BMS_HINT
	}

	// 白名单管理API路由
	whitelist := r.Group("/whitelist")
	{
		whitelist.POST("/add", middleware.VerifySignature(appConfig), handlers.AddToWhitelist(db))           // 添加到白名单
		whitelist.DELETE("/remove", middleware.VerifySignature(appConfig), handlers.RemoveFromWhitelist(db)) // 从白名单移除
		whitelist.GET("/list", middleware.VerifySignature(appConfig), handlers.GetWhitelistRecords(db))      // 获取白名单记录
		whitelist.GET("/check", middleware.VerifySignature(appConfig), handlers.CheckWhitelistStatus(db))    // 检查白名单状态（详细版本）
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	return r
}

// LogRoutes 打印路由信息
func LogRoutes() {
	log.Println("API路由信息:")
	log.Println("")
	log.Println("通用接口 (/common):")
	log.Println("  游戏配置:")
	log.Println("    GET  /common/config/info - 获取游戏启动配置 (BMS_LAUNCH_CONFIG)")
	log.Println("    GET  /common/config/share_list - 获取分享配置 (BMS_SHARE_CONFIG)")
	log.Println("  数据存储:")
	log.Println("    POST /common/game-data/s-save - 保存游戏数据 (DATA_SAVE, 需要签名验证)")
	log.Println("    GET  /common/game-data/get - 获取游戏数据 (DATA_GET)")
	log.Println("    GET  /common/game-data/multi-get - 批量获取游戏数据 (DATA_MULTIGET)")
	log.Println("")
	log.Println("检查工具 (/check):")
	log.Println("    GET  /check/time - 获取服务器时间 (BMS_SERVER_TIME)")
	log.Println("    GET  /check/ip/is - IP检查 (BMS_IP_IS_ENABLE)")
	log.Println("    GET  /check/login-code - 登录码验证 (LOGINCODE)")
	log.Println("    GET  /check/uuidInWhitelist - UUID白名单检查 (UUID_IN_WHITE_LIST, 需要签名验证)")
	log.Println("")
	log.Println("统计上报 (/statistics):")
	log.Println("    POST /statistics/login_log - 登录日志统计 (BMS_LOGIN_LOG)")
	log.Println("    POST /statistics/game - 游戏行为统计 (BMS_GAME)")
	log.Println("    POST /statistics/ad/show - 广告展示统计 (BMS_AD_SHOW)")
	log.Println("    POST /statistics/ad/hit - 广告点击统计 (BMS_AD_HIT)")
	log.Println("    POST /statistics/share/show - 分享展示统计 (BMS_SHARE_SHOW)")
	log.Println("    POST /statistics/hint - 提示统计 (BMS_HINT)")
	log.Println("")
	log.Println("白名单管理 (/whitelist, 需要签名验证):")
	log.Println("    POST   /whitelist/add - 添加到白名单")
	log.Println("    DELETE /whitelist/remove - 从白名单移除")
	log.Println("    GET    /whitelist/list - 获取白名单记录")
	log.Println("    GET    /whitelist/check - 检查白名单状态（详细版本）")
	log.Println("")
	log.Println("系统:")
	log.Println("    GET  /health - 健康检查")
}
