package database

import (
	"strconv"
)

// SQLServerAdapter SQL Server适配器，实现DatabaseInterface接口
type SQLServerAdapter struct {
	sqlServerDB *SQLServerDatabase
}

// NewSQLServerAdapter 创建新的SQL Server适配器
func NewSQLServerAdapter(server string, port int, database, username, password, instance string, encrypt bool) (*SQLServerAdapter, error) {
	sqlServerDB, err := NewSQLServerDatabase(server, port, database, username, password, instance, encrypt)
	if err != nil {
		return nil, err
	}
	return &SQLServerAdapter{sqlServerDB: sqlServerDB}, nil
}

// Close 关闭SQL Server连接
func (s *SQLServerAdapter) Close() error {
	return s.sqlServerDB.Close()
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (s *SQLServerAdapter) SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error) {
	recordID, action, err := s.sqlServerDB.SaveRecord(userID, gameName, gameData)
	if err != nil {
		return nil, "", err
	}
	// 将int64转换为string以保持与其他适配器的一致性
	return strconv.FormatInt(recordID.(int64), 10), action, nil
}

// GetRecords 获取游戏记录
func (s *SQLServerAdapter) GetRecords(userID, gameName string, limit, offset int) (interface{}, int64, error) {
	return s.sqlServerDB.GetRecords(userID, gameName, limit, offset)
}

// DeleteRecord 删除记录（实现DatabaseInterface接口）
func (s *SQLServerAdapter) DeleteRecord(userID, gameName string) error {
	return s.sqlServerDB.DeleteRecord(userID, gameName)
}

// ==================== 白名单相关方法 ====================

// CheckWhitelist 检查UUID是否在白名单中
func (s *SQLServerAdapter) CheckWhitelist(appName, uuid string) (bool, error) {
	return s.sqlServerDB.CheckWhitelist(appName, uuid)
}

// AddToWhitelist 添加UUID到白名单
func (s *SQLServerAdapter) AddToWhitelist(appName, uuid string) error {
	return s.sqlServerDB.AddToWhitelist(appName, uuid)
}

// RemoveFromWhitelist 从白名单中移除UUID
func (s *SQLServerAdapter) RemoveFromWhitelist(appName, uuid string) error {
	return s.sqlServerDB.RemoveFromWhitelist(appName, uuid)
}

// GetWhitelistRecords 获取白名单记录
func (s *SQLServerAdapter) GetWhitelistRecords(appName string, limit, offset int) (interface{}, int64, error) {
	return s.sqlServerDB.GetWhitelist(appName, limit, offset)
}
