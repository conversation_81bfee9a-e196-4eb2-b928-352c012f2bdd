package models

import "time"

// GameRecord 游戏记录结构体
type GameRecord struct {
	ID        int       `json:"id" db:"id"`
	UserID    string    `json:"user_id" db:"user_id"`
	GameName  string    `json:"game_name" db:"game_name"`
	GameData  string    `json:"game_data" db:"game_data"` // JSON格式的游戏数据
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// SaveRecordRequest 保存记录请求结构体
type SaveRecordRequest struct {
	UserID   string      `json:"user_id" binding:"required"`
	GameName string      `json:"game_name" binding:"required"`
	GameData interface{} `json:"game_data" binding:"required"` // JSON格式的游戏数据
}

// GetRecordsResponse 获取记录响应
type GetRecordsResponse struct {
	Records []GameRecord `json:"records"`
	Total   int          `json:"total"`
}

// SignatureRequest 需要签名验证的请求结构
type SignatureRequest struct {
	AppName   string `json:"app_name" form:"app_name"`
	Version   string `json:"version" form:"version"`
	UUID      string `json:"uuid" form:"uuid"`
	Timestamp int64  `json:"timestamp" form:"timestamp"`
	Nonce     string `json:"nonce" form:"nonce"`
	Sign      string `json:"sign" form:"sign"`
}

// StandardResponse 标准API响应格式
type StandardResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

// UnifiedGameRecord 统一的游戏记录结构体
type UnifiedGameRecord struct {
	ID        interface{} `json:"id"`
	UserID    string      `json:"user_id"`
	GameName  string      `json:"game_name"`
	GameData  interface{} `json:"game_data"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}

// UnifiedGetRecordsResponse 统一的获取记录响应
type UnifiedGetRecordsResponse struct {
	Records []UnifiedGameRecord `json:"records"`
	Total   int64               `json:"total"`
}

// ==================== 白名单相关结构 ====================

// Whitelist 白名单结构体
type Whitelist struct {
	ID        int       `json:"id" db:"id"`
	AppName   string    `json:"app_name" db:"app_name"`
	UUID      string    `json:"uuid" db:"uuid"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// WhitelistRequest 白名单请求结构体
type WhitelistRequest struct {
	AppName string `json:"app_name" binding:"required"`
	UUID    string `json:"uuid" binding:"required"`
}

// ==================== BaseNet API 请求结构 ====================

// SignInRequest 登录请求结构
type SignInRequest struct {
	Code    string `json:"code" form:"code"`
	AppName string `json:"app_name" form:"app_name"`
}

// DataSaveRequest DATA_SAVE请求结构
type DataSaveRequest struct {
	AppName   string `json:"app_name" form:"app_name"`
	Version   string `json:"version" form:"version"`
	UUID      string `json:"uuid" form:"uuid"`
	DKey      string `json:"d_key" form:"d_key"`
	DData     string `json:"d_data" form:"d_data"`
	Timestamp int64  `json:"timestamp" form:"timestamp"`
	Nonce     string `json:"nonce" form:"nonce"`
	Sign      string `json:"sign" form:"sign"`
}

// DataGetRequest DATA_GET请求结构
type DataGetRequest struct {
	AppName string `json:"app_name" form:"app_name"`
	Version string `json:"version" form:"version"`
	UUID    string `json:"uuid" form:"uuid"`
	DKey    string `json:"d_key" form:"d_key"`
}

// DataMultiGetRequest DATA_MULTIGET请求结构
type DataMultiGetRequest struct {
	AppName string `json:"app_name" form:"app_name"`
	Version string `json:"version" form:"version"`
	UUID    string `json:"uuid" form:"uuid"`
	DKeys   string `json:"d_keys" form:"d_keys"`
}

// LoginLogRequest 登录日志请求结构
type LoginLogRequest struct {
	AppName    string      `json:"app_name" form:"app_name"`
	Channel    string      `json:"channel" form:"channel"`
	Version    string      `json:"version" form:"version"`
	UUID       string      `json:"uuid" form:"uuid"`
	Platform   string      `json:"platform" form:"platform"`
	DeviceInfo interface{} `json:"device_info" form:"device_info"`
}

// GameStatRequest 游戏行为统计请求结构
type GameStatRequest struct {
	AppName string `json:"app_name" form:"app_name"`
	Channel string `json:"channel" form:"channel"`
	Version string `json:"version" form:"version"`
	UUID    string `json:"uuid" form:"uuid"`
	MData   string `json:"m_data" form:"m_data"`
	BT      int64  `json:"b_t" form:"b_t"`
}

// AdStatRequest 广告统计请求结构
type AdStatRequest struct {
	AppName   string `json:"app_name" form:"app_name"`
	Version   string `json:"version" form:"version"`
	UUID      string `json:"uuid" form:"uuid"`
	AdType    string `json:"ad_type" form:"ad_type"`
	AdID      string `json:"ad_id" form:"ad_id"`
	Platform  string `json:"platform" form:"platform"`
	Timestamp int64  `json:"timestamp" form:"timestamp"`
}

// LoginCodeRequest 登录码验证请求结构
type LoginCodeRequest struct {
	AppName string `json:"app_name" form:"app_name"`
	Version string `json:"version" form:"version"`
	Code    string `json:"code" form:"code"`
	CACode  string `json:"ca_code" form:"ca_code"`
}
