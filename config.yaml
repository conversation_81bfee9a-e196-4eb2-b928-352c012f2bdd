# 游戏日志服务配置文件

# 数据库配置
database:
  # 数据库类型: sqlite、mongodb 或 sqlserver
  type: sqlserver

  # SQLite配置
  sqlite:
    path: ./game_records.db

  # MongoDB配置
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records

  # SQL Server配置
  sqlserver:
    server: **********
    port: 14333
    database: gamelog
    username: sa
    password: p@ssw0rd@456
    instance: ""  # 实例名（可选）
    encrypt: true  # 是否启用加密

# 服务器配置
server:
  port: 3001
  host: 0.0.0.0
  
  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - "*"

# 日志配置
logging:
  level: info
  format: text
  output: stdout

# 安全配置
security:
  app_secret: "H3qR7sL9xTmNb4cDpKjEwv8yZCiOa2fU"

# BaseNet API配置
basenet:
  # 平台配置
  platforms:
    wechat:
      app_id: "your_wechat_app_id"
      app_secret: "your_wechat_app_secret"
    baidu:
      app_id: "your_baidu_app_id"
      app_secret: "your_baidu_app_secret"
    qq:
      app_id: "your_qq_app_id"
      app_secret: "your_qq_app_secret"

  # 游戏配置
  game_config:
    max_level: 100
    daily_reward: true
    maintenance: false
    force_update: false

  # 广告配置
  ads_config:
    banner:
      enabled: true
      refresh_interval: 30
    video:
      enabled: true
      reward_amount: 50
    interstitial:
      enabled: true
      frequency: 3