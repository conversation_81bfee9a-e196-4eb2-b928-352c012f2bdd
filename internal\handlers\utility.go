package handlers

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"time"

	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// GetServerTime BMS_SERVER_TIME - 获取服务器时间
func GetServerTime(c *gin.Context) {
	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: map[string]interface{}{
			"time": time.Now().Unix(),
		},
	})
}

// CheckIP BMS_IP_IS_ENABLE - IP检查
func CheckIP(c *gin.Context) {
	clientIP := c.ClientIP()

	// 模拟IP检查逻辑，实际应用中可以检查IP白名单/黑名单
	isEnabled := true
	if clientIP == "127.0.0.1" || clientIP == "::1" {
		isEnabled = true
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: map[string]interface{}{
			"ip":        clientIP,
			"is_enable": isEnabled,
		},
	})
}

// CheckLoginCode LOGINCODE - 登录码验证
func CheckLoginCode(c *gin.Context) {
	var req models.LoginCodeRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.AppName == "" || req.Code == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 根据用户发过来的 req.Code 生成 MD5 加密的32位字符
	hash := md5.Sum([]byte(req.Code))
	caCode := fmt.Sprintf("%x", hash)

	responseData := map[string]interface{}{
		"app_name": req.AppName,
		"version":  req.Version,
		"code":     req.Code,
		"ca_code":  caCode,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: responseData,
	})
}
